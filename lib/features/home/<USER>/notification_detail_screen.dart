import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/home/<USER>/bloc/home_bloc.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

import '../../../assets/app_assets.dart';

class NotificationDetailScreen extends StatelessWidget {
  final int newsId;

  const NotificationDetailScreen({super.key, required this.newsId});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        // Safely find the news item, handle case where it doesn't exist
        final newsItem =
            state.news.where((element) => element.id == newsId).firstOrNull;
        final eventItem =
            state.events.where((element) => element.id == newsId).firstOrNull;

        // Get the item from either news or events
        final item = newsItem ?? eventItem;

        // If item is still null, show error or loading state
        if (item == null) {
          return Scaffold(
            appBar: AppBar(
              title: Text(
                LocaleKeys.notification.tr(), // or use a default title
                style: context.textTheme.displaySmall,
              ),
            ),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Item not found', // You can localize this
                    style: context.textTheme.bodyLarge,
                  ),
                  Gap(20),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text('Go Back'),
                  ),
                ],
              ),
            ),
          );
        }

        return Scaffold(
          appBar: AppBar(
            title: Text(item.title, style: context.textTheme.displaySmall),
          ),
          body: SingleChildScrollView(
            padding: EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                eventItem == null
                    ? Padding(
                      padding: const EdgeInsets.only(bottom: 10),
                      child: Hero(
                        tag: 'news_image_${item.id}',
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(21),
                          child: CachedNetworkImage(
                            imageUrl: item.image.replaceAll(
                              'https://dev.echipta.uz/uploads/https://',
                              'https://',
                            ),
                            width: double.maxFinite,
                            height: 200,
                            fit: BoxFit.cover,
                            placeholder:
                                (context, url) => Container(
                                  width: double.maxFinite,
                                  height: 200,
                                  color: AppColors.primary.withOpacity(0.1),
                                  child: Center(
                                    child: CircularProgressIndicator(
                                      color: AppColors.primary,
                                    ),
                                  ),
                                ),
                            errorWidget:
                                (context, url, error) => Container(
                                  width: double.maxFinite,
                                  height: 200,
                                  color: AppColors.primary,
                                  child: Center(
                                    child: SvgPicture.asset(
                                      AppAssets.whiteLogo,
                                      color: AppColors.white.withOpacity(0.5),
                                      height: 50,
                                    ),
                                  ),
                                ),
                          ),
                        ),
                      ),
                    )
                    : const SizedBox(),
                Text(item.text, style: context.textTheme.bodySmall),
              ],
            ),
          ),
        );
      },
    );
  }
}
