part of 'ball_rating_bloc.dart';

class BallRatingState extends Equatable {
  final FormzSubmissionStatus ballRatingStatus;     // Ball rating API (/clients/rating)
  final FormzSubmissionStatus historyStatus;
  final FormzSubmissionStatus promotionsStatus;
  final FormzSubmissionStatus packagesStatus;
  final FormzSubmissionStatus userInfoStatus;
  final FormzSubmissionStatus purchaseStatus;

  final BallRatingResponseEntity ballRatingData;    // Consolidated ball rating data
  final List<BallHistoryEntity> history;
  final List<BallPromotionEntity> promotions;
  final List<BallPackageEntity> packages;
  final BallUserEntity currentUser;

  final int currentTabIndex;
  final int? selectedPackageId;

  const BallRatingState({
    this.ballRatingStatus = FormzSubmissionStatus.initial,
    this.historyStatus = FormzSubmissionStatus.initial,
    this.promotionsStatus = FormzSubmissionStatus.initial,
    this.packagesStatus = FormzSubmissionStatus.initial,
    this.userInfoStatus = FormzSubmissionStatus.initial,
    this.purchaseStatus = FormzSubmissionStatus.initial,
    this.ballRatingData = const BallRatingResponseEntity(),
    this.history = const [],
    this.promotions = const [],
    this.packages = const [],
    this.currentUser = const BallUserEntity(),
    this.currentTabIndex = 0,
    this.selectedPackageId,
  });

  BallRatingState copyWith({
    FormzSubmissionStatus? ballRatingStatus,
    FormzSubmissionStatus? historyStatus,
    FormzSubmissionStatus? promotionsStatus,
    FormzSubmissionStatus? packagesStatus,
    FormzSubmissionStatus? userInfoStatus,
    FormzSubmissionStatus? purchaseStatus,
    BallRatingResponseEntity? ballRatingData,
    List<BallHistoryEntity>? history,
    List<BallPromotionEntity>? promotions,
    List<BallPackageEntity>? packages,
    BallUserEntity? currentUser,
    int? currentTabIndex,
    int? selectedPackageId,
  }) {
    return BallRatingState(
      ballRatingStatus: ballRatingStatus ?? this.ballRatingStatus,
      historyStatus: historyStatus ?? this.historyStatus,
      promotionsStatus: promotionsStatus ?? this.promotionsStatus,
      packagesStatus: packagesStatus ?? this.packagesStatus,
      userInfoStatus: userInfoStatus ?? this.userInfoStatus,
      purchaseStatus: purchaseStatus ?? this.purchaseStatus,
      ballRatingData: ballRatingData ?? this.ballRatingData,
      history: history ?? this.history,
      promotions: promotions ?? this.promotions,
      packages: packages ?? this.packages,
      currentUser: currentUser ?? this.currentUser,
      currentTabIndex: currentTabIndex ?? this.currentTabIndex,
      selectedPackageId: selectedPackageId ?? this.selectedPackageId,
    );
  }

  @override
  List<Object?> get props => [
        ballRatingStatus,
        ballRatingData,
        historyStatus,
        promotionsStatus,
        packagesStatus,
        userInfoStatus,
        purchaseStatus,
        history,
        promotions,
        packages,
        currentUser,
        currentTabIndex,
        selectedPackageId,
      ];
}
