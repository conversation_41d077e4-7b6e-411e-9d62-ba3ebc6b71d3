import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:formz/formz.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/ball_rating/presentation/bloc/ball_rating_bloc.dart';
import 'package:echipta/features/common/widgets/w_empty.dart';

import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:gap/gap.dart';
import 'package:data_table_2/data_table_2.dart';

class BallRatingView extends StatelessWidget {
  const BallRatingView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<BallRatingBloc, BallRatingState>(
      listener: (context, state) {
        log('BallRatingView - State changed: ${state.ballRatingStatus}, clients: ${state.ballRatingData.top_clients.length}');
      },
      builder: (context, ballRatingState) {
        // Debug logging
        log('BallRatingView - ballRatingStatus: ${ballRatingState.ballRatingStatus}');
        log('BallRatingView - top_clients count: ${ballRatingState.ballRatingData.top_clients.length}');
        // Additional debug for success state
        if (ballRatingState.ballRatingStatus == FormzSubmissionStatus.success) {
          log('BallRatingView - SUCCESS STATE: ${ballRatingState.ballRatingData.top_clients.length} clients loaded');
        }

        // Check ball rating status
        final isLoading = ballRatingState.ballRatingStatus == FormzSubmissionStatus.inProgress;
        final hasError = ballRatingState.ballRatingStatus == FormzSubmissionStatus.failure;
        final hasData = ballRatingState.ballRatingData.top_clients.isNotEmpty;

        // Show loading only if we're actively loading and have no data yet
        final shouldShowLoading = isLoading && !hasData;

        if (shouldShowLoading) {
          return const Center(child: CircularProgressIndicator.adaptive());
        } else if (hasError && !hasData) {
          return RefreshIndicator(
            onRefresh: () async {
              context.read<BallRatingBloc>().add(const GetBallRatingEvent());
              context.read<BallRatingBloc>().add(const GetCurrentUserBallInfoEvent());
            },
            child: SingleChildScrollView(
              physics: const NeverScrollableScrollPhysics(),
              child: SizedBox(
                height: MediaQuery.of(context).size.height * 0.6,
                child: const Center(child: Text("Xatolik yuz berdi! Tortib yangilang")),
              ),
            ),
          );
        } else if (!hasData) {
          return RefreshIndicator(
            onRefresh: () async {
              context.read<BallRatingBloc>().add(const GetBallRatingEvent());
              context.read<BallRatingBloc>().add(const GetCurrentUserBallInfoEvent());
              },
            child: SingleChildScrollView(
              physics: const NeverScrollableScrollPhysics(),
              child: SizedBox(
                height: MediaQuery.of(context).size.height * 0.6,
                child: WEmptyScreen(),
              ),
            ),
          );
        }

        return BlocBuilder<ProfileBloc, ProfileState>(
          builder: (context, profileState) {
            return DataTable2(
                columnSpacing: 0,
                horizontalMargin: 0,
              minWidth: MediaQuery.of(context).size.width,
                dataRowHeight: 56,
                headingRowHeight: 48,
                ///Bottom padding to the column
                bottomMargin: context.padding.bottom+10,
                headingRowDecoration: const BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: AppColors.primary, width: 2),
                  ),
                ),
                headingRowColor: WidgetStateProperty.all(AppColors.fillColor),
                decoration: const BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: AppColors.mediumGrey, width: 1),
                  ),
                ),
                columns: [
                  DataColumn2(
                    label: Padding(
                      padding: const EdgeInsets.only(left: 20),
                      child: Text(
                        "O'rni",
                        style: context.textTheme.bodySmall!.copyWith(
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF1B1B1B),
                          fontSize: 13,
                        ),
                      ),
                    ),
                    size: ColumnSize.S,
                  ),
                  DataColumn2(
                    fixedWidth: 160,
                    label: Text(
                      "Muxlislar",
                      style: context.textTheme.bodySmall!.copyWith(
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF1B1B1B),
                        fontSize: 13,
                      ),
                    ),
                    size: ColumnSize.L,
                  ),
                  DataColumn2(
                    label: Center(
                      child: Text(
                        "O'yin",
                        style: context.textTheme.bodySmall!.copyWith(
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF1B1B1B),
                          fontSize: 13,
                        ),
                      ),
                    ),
                    size: ColumnSize.S,
                  ),
                  DataColumn2(
                    label: Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            "Ball",
                            style: context.textTheme.bodySmall!.copyWith(
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF1B1B1B),
                              fontSize: 13,
                            ),
                          ),
                        ],
                      ),
                    ),
                    size: ColumnSize.S,
                  ),
                  DataColumn2(
                    fixedWidth: 50,
                    label: Stack(
                      alignment: Alignment.center,
                      children: [
                        ///Up and down should be one each after
                        Positioned(
                          top: 5,
                          child: Icon(
                            Icons.arrow_drop_up,
                            size: 30,
                            color: const Color(0xFF06BC49),
                          ),
                        ),
                        Positioned(
                          bottom: 5,
                          child: Icon(
                            Icons.arrow_drop_down,
                            size: 30,
                            color: const Color(0xFFEB1F28),
                          ),
                        ),
                      ],
                    ),
                    size: ColumnSize.S,
                  ),
                ],
                rows: _buildDataRows(context, ballRatingState, profileState),
              );
          },
        );
      },
    );
  }

  List<DataRow2> _buildDataRows(BuildContext context, BallRatingState ballRatingState, ProfileState profileState) {
    // Use new ball rating data if available, fallback to legacy data
    if (ballRatingState.ballRatingData.top_clients.isNotEmpty) {
      return ballRatingState.ballRatingData.top_clients.map((client) {
        final isCurrentUser = client.is_current_user || client.client_id == profileState.me.id;

        return DataRow2(
          decoration: BoxDecoration(
            gradient: isCurrentUser
                ? const LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                Color(0xFF06BC49),
                Color(0x00D9D9D9),
              ],
            )
                : null,
          ),
          cells: [
            // Position cell - use rank from API instead of calculating from index
            DataCell(
              Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '${client.rank}',
                      style: context.textTheme.bodySmall!.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    const Gap(20),
                    VerticalDivider(
                      color: AppColors.mediumGrey,
                      width: 2,
                      thickness: 2,
                      indent: 15,
                      endIndent: 15,
                    ),
                  ],
                ),
              ),
            ),
            // Name cell with avatar
            DataCell(
              Row(
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                    ),
                    child: ClipOval(
                      child: CachedNetworkImage(
                        imageUrl: isCurrentUser
                            ? profileState.me.picture
                            : "https://picsum.photos/200",
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: AppColors.mediumGrey,
                          child: const Icon(
                            Icons.person,
                            color: AppColors.white,
                            size: 12,
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: AppColors.mediumGrey,
                          child: const Icon(
                            Icons.person,
                            color: AppColors.white,
                            size: 12,
                          ),
                        ),
                      ),
                    ),
                  ),
                  const Gap(8),
                  Flexible(
                    child: Text(
                      client.full_name,
                      style: context.textTheme.bodySmall!.copyWith(
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF342783),
                        fontSize: 12,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
            // Games cell
            DataCell(
              Center(
                child: Text(
                  '${client.game}',
                  style: context.textTheme.bodySmall!.copyWith(
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF342783),
                    fontSize: 14,
                  ),
                ),
              ),
            ),
            // Score cell
            DataCell(
              Center(
                child: Text(
                  '${client.rating}',
                  style: context.textTheme.bodySmall!.copyWith(
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF342783),
                    fontSize: 14,
                  ),
                ),
              ),
            ),
            // Change indicator cell
            DataCell(
              Center(
                child: Icon(
                  Icons.remove,
                  size: 16,
                  color: AppColors.mediumGrey,
                ),
              ),
            ),
          ],
        );
      }).toList();
    }

    // Return empty list if no data
    return [];
  }
}
