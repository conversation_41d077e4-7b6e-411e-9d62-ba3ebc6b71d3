import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/services/notification_service.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/profile/presentation/widgets/w_profile_item.dart';
import 'package:echipta/features/profile/presentation/widgets/w_profile_user_data.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        foregroundColor: AppColors.black,
        title: Text(
          LocaleKeys.settings.tr(),
          style: context.textTheme.displaySmall!.copyWith(
            color: AppColors.black,
          ),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.fromLTRB(20, 10, 20, 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            WProfileItem(
              title: LocaleKeys.appPin.tr(),
              icon: AppAssets.lock,
              route: AppRouter.setPincode,
            ),
            Gap(16),
            WProfileItem(
              title: LocaleKeys.appLang.tr(),
              icon: AppAssets.language,
              route: AppRouter.lang,
            ),
            // Debug notification buttons (only in debug mode)
            if (kDebugMode) ...[
              Gap(16),
              Text(
                "Test Notifications",
                style: context.textTheme.titleMedium!.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.black,
                ),
              ),
              Gap(12),
              _buildTestNotificationButton(
                context,
                title: "No Type",
                subtitle: "Default behavior",
                color: AppColors.grey,
                onTap: () => NotificationService().showTestNotificationWithoutType(),
              ),
              Gap(12),
              _buildTestNotificationButton(
                context,
                title: "Event Notification",
                subtitle: "Should go to tab 1",
                color: AppColors.green,
                onTap: () => NotificationService().showTestEventNotification(),
              ),
              Gap(12),
              _buildTestNotificationButton(
                context,
                title: "Regular Notification",
                subtitle: "Should go to tab 0",
                color: AppColors.primary2,
                onTap: () => NotificationService().showTestRegularNotification(),
              ),
              Gap(12),
              _buildTestNotificationButton(
                context,
                title: "With Image",
                subtitle: "Image notification",
                color: AppColors.yellow,
                onTap: () => NotificationService().showTestNotificationWithImage(),
              ),
              Gap(12),
              _buildTestNotificationButton(
                context,
                title: "Ticket Type",
                subtitle: "Should go to tickets",
                color: AppColors.red,
                onTap: () => NotificationService().showTestTicketNotification(),
              ),
              Gap(12),
              _buildTestNotificationButton(
                context,
                title: "Order Type",
                subtitle: "Should go to order history",
                color: AppColors.warning,
                onTap: () => NotificationService().showTestOrderNotification(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTestNotificationButton(
    BuildContext context, {
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color, width: 1),
        ),
        child: Row(
          children: [
            Icon(Icons.notifications_active, color: color),
            Gap(12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: color,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: color.withOpacity(0.7),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: color, size: 16),
          ],
        ),
      ),
    );
  }
}
