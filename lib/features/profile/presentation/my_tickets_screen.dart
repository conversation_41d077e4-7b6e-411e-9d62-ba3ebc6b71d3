import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/common/widgets/w_empty.dart';
import 'package:echipta/features/home/<USER>/bloc/home_bloc.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:echipta/features/profile/presentation/widgets/w_my_tickets_item.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';
import 'package:grouped_list/grouped_list.dart';

class TicketsScreen extends StatefulWidget {
  const TicketsScreen({super.key});

  @override
  State<TicketsScreen> createState() => _TicketsScreenState();
}

class _TicketsScreenState extends State<TicketsScreen> {
  @override
  void initState() {
    super.initState();
    context.read<ProfileBloc>().add(GetMyTicketsEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        foregroundColor: AppColors.white,
        backgroundColor: AppColors.primary,
        surfaceTintColor: AppColors.primary,
        title: Text(
          LocaleKeys.mytickets.tr(),
          style: context.textTheme.displaySmall!.copyWith(
            color: AppColors.white,
          ),
        ),
      ),
      body: BlocBuilder<ProfileBloc, ProfileState>(
        builder: (context, state) {
          if (state.myTicketsStatus.isInProgress) {
            return const Center(child: CircularProgressIndicator.adaptive());
          } else if (state.myTicketsStatus.isFailure) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    height: MediaQuery.of(context).size.height * 0.4,
                    child: SvgPicture.asset(AppAssets.errorIll),
                  ),
                  const Text("Xatolik yuz berdi qayta urinib ko'ring"),
                  const Gap(20),
                  WButton(
                    onTap: () {
                      context.read<ProfileBloc>().add(GetMyTicketsEvent());
                    },
                    txt: "Qayta urinib ko'rish",
                  ),
                ],
              ),
            );
          } else if (state.myTicketsStatus.isSuccess &&
              state.myTickets.isEmpty) {
            return WEmptyScreen();
          } else {
            return GroupedListView(
              padding: const EdgeInsets.all(20),
              elements: state.myTickets,

              ///Format should be like 31 August, implement null case too
              groupBy:
                  (element) =>
                      DateFormat("dd MMMM", context.locale.languageCode).format(
                        element.match.start_date?.toLocal() ?? DateTime.now(),
                      ),
              groupSeparatorBuilder: (value) {
                return Text(value, style: context.textTheme.headlineLarge);
              },
              itemBuilder: (context, element) {
                return WMyTicketsItem(item: element);
              },
            );
          }
        },
      ),
    );
  }
}
