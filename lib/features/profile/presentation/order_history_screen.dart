import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/cart/presentation/widgets/w_orders_tab.dart';
import 'package:echipta/features/common/widgets/w_empty.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:echipta/features/profile/presentation/widgets/w_products_history.dart';
import 'package:echipta/features/profile/presentation/widgets/w_tickets_history.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';

class OrderHistoryScreen extends StatefulWidget {
  const OrderHistoryScreen({super.key});

  @override
  State<OrderHistoryScreen> createState() => _OrderHistoryScreenState();
}

class _OrderHistoryScreenState extends State<OrderHistoryScreen> {
  @override
  void initState() {
    super.initState();
    context.read<OrderBloc>().add(GetOrdersEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        foregroundColor: AppColors.white,
        backgroundColor: AppColors.primary,
        surfaceTintColor: AppColors.primary,
        title: Text(
          "Buyurtmalar tarixi",
          style: context.textTheme.displaySmall!.copyWith(
            color: AppColors.white,
          ),
        ),
      ),
      body: BlocBuilder<OrderBloc, OrderState>(
        builder: (context, state) {
          if (state.ordersStatus.isInProgress) {
            return const Center(child: CircularProgressIndicator.adaptive());
          } else if (state.ordersStatus.isFailure) {
            return const Center(child: Text("Xatolik yuz berdi!"));
          } else if (state.orderIdStatus.isSuccess && state.orders.isEmpty) {
            return WEmptyScreen();
          } else {
            final tickets =
                state.orders.where((element) => element.type == 1).toList();
            final products =
                state.orders.where((element) => element.type == 2).toList();
            if (kDebugMode) {
              print(tickets);
              print(products);
            }
            return DefaultTabController(
              length: 2,
              child: Column(
                children: [
                  const TabBar(
                    labelStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.w700),
                    dividerHeight: 0,
                    tabs: [Tab(text: "Chiptalar"), Tab(text: "Maxsulotlar")],
                  ),
                  Expanded(
                    child: TabBarView(
                      children: [
                        WTicketsHistory(tickets: tickets),
                        // WProductsHistory(products: products),
                        WOrdersTab()
                      ],
                    ),
                  ),
                ],
              ),
            );
          }
        },
      ),
    );
  }
}
